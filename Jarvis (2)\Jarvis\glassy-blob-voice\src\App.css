body, html, #root, .App {
  min-height: 100vh;
  margin: 0;
  padding: 0;
  font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
  background: #101014;
  box-sizing: border-box;
}

.App {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 0;
  background: none;
}

h1 {
  font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
  font-weight: 800;
  letter-spacing: -1px;
  margin-bottom: 0.5em;
  text-shadow: 0 2px 16px #b0c4de88;
}

button {
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  background: rgba(255,255,255,0.12);
  color: #fff;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  padding: 0.6rem 1.2rem;
  box-shadow: 0 2px 8px #b0c4de33;
  margin-bottom: 0.5rem;
}

button:active {
  box-shadow: 0 2px 16px #b0c4de44;
}

.glass-card {
  background: rgba(255,255,255,0.08);
  border-radius: 18px;
  box-shadow: 0 4px 32px #b0c4de33;
  padding: 24px;
  color: #e0e0e0;
  font-size: 20px;
  font-weight: 500;
  backdrop-filter: blur(12px);
  border: 1.5px solid rgba(255,255,255,0.18);
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
