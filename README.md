# 🎤 Jarvis Voice AI

A beautiful voice-responsive 3D application with AI chat capabilities. Features real-time speech recognition, stunning 3D visualizations, and support for multiple free AI providers.

![Voice Responsive 3D App](https://img.shields.io/badge/React-18-blue) ![TypeScript](https://img.shields.io/badge/TypeScript-5-blue) ![Vite](https://img.shields.io/badge/Vite-5-purple) ![Three.js](https://img.shields.io/badge/Three.js-0.159-green)

## ✨ Features

- 🎤 **Real-time Voice Recognition** - Browser-based speech-to-text
- 🎨 **3D Voice Visualization** - Beautiful reactive sphere with shader effects
- 🤖 **AI Chat Integration** - Multiple AI provider support
- 🆓 **Free AI Options** - Several completely free providers available
- ⚡ **Fast Performance** - Built with Vite for optimal speed
- 🎯 **TypeScript** - Full type safety and better DX
- 📱 **Responsive Design** - Works on desktop and mobile

## 🚀 Quick Start

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd jarvis-voice-ai
   npm install
   ```

2. **Configure AI Provider** (See [Free AI Providers Guide](docs/FREE_AI_PROVIDERS.md))
   - Get a free API key from OpenRouter, Groq, or Hugging Face
   - Open app settings and configure your provider

3. **Run Development Server**
   ```bash
   npm run dev
   ```

4. **Open in Browser**
   - Navigate to `http://localhost:5173`
   - Allow microphone permissions
   - Start speaking or chatting!

## 🤖 Supported AI Providers

| Provider | Cost | Speed | Models | Setup Difficulty |
|----------|------|-------|--------|------------------|
| **OpenRouter** ⭐ | Free tier | Fast | Many free models | Easy |
| **Groq** | Free tier | Very fast | Limited free | Easy |
| **Hugging Face** | Free tier | Moderate | Open source | Easy |
| **OpenAI** | Paid | Fast | GPT models | Easy |

> ⭐ **Recommended**: OpenRouter offers the best balance of free models and performance.

## 🛠️ Technology Stack

- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **3D Graphics**: Three.js with custom shaders
- **Audio**: Web Audio API + Speech Recognition API
- **Testing**: Vitest + React Testing Library

## 📁 Project Structure

```
src/
├── components/
│   ├── VoiceVisualizer.tsx    # Main component (needs refactoring)
│   └── VoiceVisualizer.test.tsx
├── test/
│   └── setup.ts               # Test configuration
├── App.tsx                    # Root component
├── main.tsx                   # Entry point
└── index.css                  # Global styles
```

## 🔧 Available Scripts

```bash
npm run dev        # Start development server
npm run build      # Build for production
npm run preview    # Preview production build
npm run test       # Run tests
npm run test:ui    # Run tests with UI
npm run lint       # Lint code
```

## 🌐 Browser Support

- ✅ **Chrome** (Recommended - full speech recognition support)
- ✅ **Edge** (Full support)
- ✅ **Safari** (Full support)
- ⚠️ **Firefox** (Limited speech recognition support)

## 📚 Documentation

- [Setup Guide](docs/SETUP.md)
- [Free AI Providers](docs/FREE_AI_PROVIDERS.md)
- [Project Reorganization Plan](PROJECT_REORGANIZATION_PLAN.md)

## 🚧 Known Issues & Improvements Needed

- [ ] Large component needs refactoring (VoiceVisualizer.tsx is 1200+ lines)
- [ ] Remove duplicate project directories
- [ ] Add proper error boundaries
- [ ] Implement custom hooks for complex logic
- [ ] Add more comprehensive tests
- [ ] Improve TypeScript types

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🙏 Acknowledgments

- Three.js community for 3D graphics inspiration
- OpenRouter for providing free AI model access
- React and Vite teams for excellent developer tools
