# Free AI Providers Setup Guide

This project now supports multiple AI providers, including several **completely free** options! Here's how to set them up:

## 🆓 Recommended Free Options

### 1. OpenRouter (Best Option)
- **Website**: https://openrouter.ai/
- **Free Models**: Llama 3.2, Phi-3, Gemma 2, Mistral 7B
- **Setup**:
  1. Sign up at openrouter.ai
  2. Go to "Keys" section
  3. Create a new API key
  4. In the app settings, select "OpenRouter" as provider
  5. Paste your API key (starts with `sk-or-`)
  6. Choose a free model (marked with "free" in the name)

### 2. Groq (Very Fast)
- **Website**: https://console.groq.com/
- **Free Models**: Llama 3.2, Gemma 2, Mixtral
- **Setup**:
  1. Sign up at console.groq.com
  2. Go to API Keys section
  3. Create a new API key
  4. In the app settings, select "Groq" as provider
  5. Paste your API key (starts with `gsk_`)

### 3. Hugging Face (Open Source Models)
- **Website**: https://huggingface.co/
- **Free Models**: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Phi-2
- **Setup**:
  1. Sign up at huggingface.co
  2. Go to Settings > Access Tokens
  3. Create a new token with "Read" permissions
  4. In the app settings, select "Hugging Face" as provider
  5. Paste your token (starts with `hf_`)

## 🔧 How to Switch Providers

1. Click the **Settings** button (gear icon) in the top-right
2. Select your preferred **AI Provider** from the dropdown
3. Enter your **API Key** for that provider
4. Choose an **AI Model** (free models are clearly marked)
5. Enable **Chatbot Mode** if you want to chat
6. Click **Save**

## 💡 Tips

- **OpenRouter** is recommended as it has the most free models and best compatibility
- **Groq** is extremely fast but has fewer model options
- **Hugging Face** models may be slower but are completely open source
- You can switch between providers anytime in settings
- Free tiers usually have rate limits, but they're generous for personal use

## 🚀 Getting Started

1. Choose a provider from the list above
2. Sign up and get your free API key
3. Configure it in the app settings
4. Start chatting with AI for free!

No credit card required for any of these free options!
