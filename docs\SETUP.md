# Jarvis Voice AI - Setup Guide

## Quick Start

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Configure AI Provider**
   - Open the app settings (gear icon)
   - Select your preferred AI provider
   - Add your API key
   - Choose a model

3. **Run Development Server**
   ```bash
   npm run dev
   ```

4. **Build for Production**
   ```bash
   npm run build
   ```

## Features

- 🎤 **Voice Recognition**: Real-time speech-to-text
- 🎨 **3D Visualization**: Beautiful voice-reactive 3D sphere
- 🤖 **AI Chat**: Multiple AI provider support
- 🆓 **Free Options**: Several completely free AI providers
- ⚡ **Fast**: Built with Vite for optimal performance

## AI Providers Supported

- **OpenRouter** (Recommended) - Many free models
- **Groq** - Fast inference with free tier
- **Hugging Face** - Open source models
- **OpenAI** - Premium models (paid)

## Project Structure

```
src/
├── components/     # React components
├── hooks/         # Custom React hooks
├── services/      # API and external services
├── utils/         # Utility functions
└── __tests__/     # Test files
```

## Development

- **Framework**: React 18 + TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **3D Graphics**: Three.js
- **Testing**: Vitest + React Testing Library

## Browser Support

- Chrome (recommended)
- Edge
- Safari
- Firefox (limited speech recognition support)
