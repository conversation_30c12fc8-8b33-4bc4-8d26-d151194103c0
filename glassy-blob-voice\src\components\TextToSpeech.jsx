import React, { useState } from 'react';

const SAMPLE_TEXT = 'This is a sample text being spoken by the system.';

export default function TextToSpeech({ onSpeaking }) {
  const [speaking, setSpeaking] = useState(false);

  const speak = () => {
    if (!('speechSynthesis' in window)) return;
    const utter = new window.SpeechSynthesisUtterance(SAMPLE_TEXT);
    utter.onstart = () => {
      setSpeaking(true);
      onSpeaking && onSpeaking(true);
    };
    utter.onend = () => {
      setSpeaking(false);
      onSpeaking && onSpeaking(false);
    };
    window.speechSynthesis.speak(utter);
  };

  return (
    <div style={{ margin: '1rem 0' }}>
      <button onClick={speak} style={{ padding: '0.5rem 1rem', borderRadius: 8, background: '#b388ff', color: '#fff', border: 'none', fontWeight: 'bold' }}>
        Speak Sample Text
      </button>
      <div style={{ marginTop: 12, fontSize: 18, color: '#555' }}>
        {speaking ? 'Speaking...' : ''}
      </div>
    </div>
  );
} 