import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
import { Mi<PERSON>, MicOff, AlertCircle, Settings, X, Save, MessageCircle, Send, ChevronDown } from 'lucide-react';

const VoiceVisualizer: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const sceneRef = useRef<{
    scene: THREE.Scene;
    camera: THREE.PerspectiveCamera;
    renderer: THREE.WebGLRenderer;
    mesh: THREE.Mesh;
    uniforms: any;
    animationId: number;
  } | null>(null);
  
  const [isListening, setIsListening] = useState(false);
  const [pitch, setPitch] = useState(0);
  const [volume, setVolume] = useState(0);
  const [isSupported, setIsSupported] = useState(true);
  const [currentWord, setCurrentWord] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [apiKey, setApiKey] = useState('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');
  const [selectedModel, setSelectedModel] = useState('mistralai/mistral-7b-instruct:free');
  const [selectedProvider, setSelectedProvider] = useState('openrouter');
  const [showModelDropdown, setShowModelDropdown] = useState(false);
  const [showProviderDropdown, setShowProviderDropdown] = useState(false);
  const [chatbotMode, setChatbotMode] = useState(false);
  const [chatMessages, setChatMessages] = useState<Array<{role: 'user' | 'assistant', content: string}>>([]);
  const [chatInput, setChatInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Available providers
  const availableProviders = [
    {
      id: 'openrouter',
      name: 'OpenRouter',
      description: 'Access to many free models',
      apiUrl: 'https://openrouter.ai/api/v1/chat/completions',
      requiresKey: true,
      keyPlaceholder: 'sk-or-...'
    },
    {
      id: 'huggingface',
      name: 'Hugging Face',
      description: 'Free inference API',
      apiUrl: 'https://api-inference.huggingface.co/models',
      requiresKey: true,
      keyPlaceholder: 'hf_...'
    },
    {
      id: 'groq',
      name: 'Groq',
      description: 'Fast free inference',
      apiUrl: 'https://api.groq.com/openai/v1/chat/completions',
      requiresKey: true,
      keyPlaceholder: 'gsk_...'
    },
    {
      id: 'openai',
      name: 'OpenAI',
      description: 'Original OpenAI API (paid)',
      apiUrl: 'https://api.openai.com/v1/chat/completions',
      requiresKey: true,
      keyPlaceholder: 'sk-...'
    }
  ];

  // Available models by provider
  const availableModels = {
    openrouter: [
      { id: 'meta-llama/llama-3.2-3b-instruct:free', name: 'Llama 3.2 3B', description: 'Fast and free Meta model' },
      { id: 'meta-llama/llama-3.2-1b-instruct:free', name: 'Llama 3.2 1B', description: 'Lightweight free model' },
      { id: 'microsoft/phi-3-mini-128k-instruct:free', name: 'Phi-3 Mini', description: 'Microsoft free model' },
      { id: 'google/gemma-2-9b-it:free', name: 'Gemma 2 9B', description: 'Google free model' },
      { id: 'mistralai/mistral-7b-instruct:free', name: 'Mistral 7B', description: 'Mistral free model' },
      { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'OpenAI model (paid)' },
      { id: 'gpt-4o-mini', name: 'GPT-4o Mini', description: 'OpenAI model (paid)' },
    ],
    huggingface: [
      { id: 'microsoft/DialoGPT-medium', name: 'DialoGPT Medium', description: 'Conversational AI model' },
      { id: 'microsoft/DialoGPT-large', name: 'DialoGPT Large', description: 'Larger conversational model' },
      { id: 'facebook/blenderbot-400M-distill', name: 'BlenderBot 400M', description: 'Facebook chatbot model' },
      { id: 'microsoft/phi-2', name: 'Phi-2', description: 'Microsoft small language model' },
    ],
    groq: [
      { id: 'llama-3.2-3b-preview', name: 'Llama 3.2 3B', description: 'Fast Llama model' },
      { id: 'llama-3.2-1b-preview', name: 'Llama 3.2 1B', description: 'Lightweight Llama model' },
      { id: 'gemma2-9b-it', name: 'Gemma 2 9B', description: 'Google Gemma model' },
      { id: 'mixtral-8x7b-32768', name: 'Mixtral 8x7B', description: 'Mistral mixture model' },
    ],
    openai: [
      { id: 'gpt-4o', name: 'GPT-4o', description: 'Most capable model, best for complex tasks' },
      { id: 'gpt-4o-mini', name: 'GPT-4o Mini', description: 'Faster and more affordable GPT-4o' },
      { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: 'Previous generation flagship model' },
      { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'Fast and affordable for simple tasks' },
    ]
  };
  
  // Smoothing variables
  const smoothedPitchRef = useRef(0);
  const smoothedVolumeRef = useRef(0);
  
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const animationFrameRef = useRef<number>();
  const wordTimeoutRef = useRef<NodeJS.Timeout>();
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const lastTranscriptRef = useRef('');

  // Enhanced Vertex Shader with smoother animations
  const vertexShader = `
    varying vec2 vUv;
    varying vec3 vPos;
    varying vec3 vNormal;
    varying vec3 vWorldPos;
    varying float waveHeight;
    uniform float u_time;
    uniform float u_pitch;
    uniform float u_volume;
    
    vec4 permute(vec4 x){return mod(((x*34.0)+1.0)*x, 289.0);}
    float permute(float x){return floor(mod(((x*34.0)+1.0)*x, 289.0));}
    vec4 taylorInvSqrt(vec4 r){return 1.79284291400159 - 0.85373472095314 * r;}
    float taylorInvSqrt(float r){return 1.79284291400159 - 0.85373472095314 * r;}

    vec4 grad4(float j, vec4 ip){
      const vec4 ones = vec4(1.0, 1.0, 1.0, -1.0);
      vec4 p,s;
      p.xyz = floor( fract (vec3(j) * ip.xyz) * 7.0) * ip.z - 1.0;
      p.w = 1.5 - dot(abs(p.xyz), ones.xyz);
      s = vec4(lessThan(p, vec4(0.0)));
      p.xyz = p.xyz + (s.xyz*2.0 - 1.0) * s.www; 
      return p;
    }

    float snoise(vec4 v){
      const vec2  C = vec2( 0.138196601125010504, 0.309016994374947451);
      vec4 i  = floor(v + dot(v, C.yyyy) );
      vec4 x0 = v -   i + dot(i, C.xxxx);
      vec4 i0;
      vec3 isX = step( x0.yzw, x0.xxx );
      vec3 isYZ = step( x0.zww, x0.yyz );
      i0.x = isX.x + isX.y + isX.z;
      i0.yzw = 1.0 - isX;
      i0.y += isYZ.x + isYZ.y;
      i0.zw += 1.0 - isYZ.xy;
      i0.z += isYZ.z;
      i0.w += 1.0 - isYZ.z;
      vec4 i3 = clamp( i0, 0.0, 1.0 );
      vec4 i2 = clamp( i0-1.0, 0.0, 1.0 );
      vec4 i1 = clamp( i0-2.0, 0.0, 1.0 );
      vec4 x1 = x0 - i1 + 1.0 * C.xxxx;
      vec4 x2 = x0 - i2 + 2.0 * C.xxxx;
      vec4 x3 = x0 - i3 + 3.0 * C.xxxx;
      vec4 x4 = x0 - 1.0 + 4.0 * C.xxxx;
      i = mod(i, 289.0); 
      float j0 = permute( permute( permute( permute(i.w) + i.z) + i.y) + i.x);
      vec4 j1 = permute( permute( permute( permute (
                   i.w + vec4(i1.w, i2.w, i3.w, 1.0 ))
               + i.z + vec4(i1.z, i2.z, i3.z, 1.0 ))
               + i.y + vec4(i1.y, i2.y, i3.y, 1.0 ))
               + i.x + vec4(i1.x, i2.x, i3.x, 1.0 ));
      vec4 ip = vec4(1.0/294.0, 1.0/49.0, 1.0/7.0, 0.0) ;
      vec4 p0 = grad4(j0,   ip);
      vec4 p1 = grad4(j1.x, ip);
      vec4 p2 = grad4(j1.y, ip);
      vec4 p3 = grad4(j1.z, ip);
      vec4 p4 = grad4(j1.w, ip);
      vec4 norm = taylorInvSqrt(vec4(dot(p0,p0), dot(p1,p1), dot(p2, p2), dot(p3,p3)));
      p0 *= norm.x;
      p1 *= norm.y;
      p2 *= norm.z;
      p3 *= norm.w;
      p4 *= taylorInvSqrt(dot(p4,p4));
      vec3 m0 = max(0.6 - vec3(dot(x0,x0), dot(x1,x1), dot(x2,x2)), 0.0);
      vec2 m1 = max(0.6 - vec2(dot(x3,x3), dot(x4,x4)            ), 0.0);
      m0 = m0 * m0;
      m1 = m1 * m1;
      return 49.0 * ( dot(m0*m0, vec3( dot( p0, x0 ), dot( p1, x1 ), dot( p2, x2 )))
                   + dot(m1*m1, vec2( dot( p3, x3 ), dot( p4, x4 ) ) ) ) ;
    }
    
    float fbm (in vec4 v) {
      float value = 0.0;
      float amplitude = .5;
      for (int i = 0; i < 3; i++) {
        value += amplitude * snoise(v);
        v *= 2.;
        amplitude *= .5;
      }
      return value;
    }
    
    void main() {
      vUv = uv;
      
      // Smoother pitch and volume influence
      float pitchInfluence = smoothstep(0.0, 500.0, u_pitch) * 0.5 + 0.8;
      float volumeInfluence = smoothstep(0.0, 0.3, u_volume) * 0.8 + 0.2;
      
      waveHeight = 0.25 * volumeInfluence;
      float spikes = 0.8 * pitchInfluence;
      float freq = 1.8 + u_pitch * 0.003;
      
      // Slower time progression for smoother animation
      float slowTime = u_time * 0.3;
      
      // Multi-layered noise for more organic movement
      float n1 = fbm(vec4(position.x * spikes, position.y * spikes, position.z * spikes, slowTime));
      float n2 = fbm(vec4(position.x * spikes * 0.5, position.y * spikes * 0.5, position.z * spikes * 0.5, slowTime * 1.5));
      float n = mix(n1, n2, 0.4);
      
      float scalePos = sin(n * freq) * (waveHeight * 0.3) + (1.0 + waveHeight * 0.2);
      vec3 pos = position * scalePos;
      
      vPos = pos;
      vWorldPos = (modelMatrix * vec4(pos, 1.0)).xyz;
      
      // Calculate smooth normals for better lighting
      vec3 tangent = normalize(cross(normal, vec3(0.0, 1.0, 0.0)));
      vec3 bitangent = normalize(cross(normal, tangent));
      float delta = 0.01;
      
      vec3 neighbor1 = position + tangent * delta;
      vec3 neighbor2 = position + bitangent * delta;
      
      float n1_offset = fbm(vec4(neighbor1.x * spikes, neighbor1.y * spikes, neighbor1.z * spikes, slowTime));
      float n2_offset = fbm(vec4(neighbor2.x * spikes, neighbor2.y * spikes, neighbor2.z * spikes, slowTime));
      
      vec3 pos1 = neighbor1 * (sin(n1_offset * freq) * (waveHeight * 0.3) + (1.0 + waveHeight * 0.2));
      vec3 pos2 = neighbor2 * (sin(n2_offset * freq) * (waveHeight * 0.3) + (1.0 + waveHeight * 0.2));
      
      vec3 calculatedNormal = normalize(cross(pos1 - pos, pos2 - pos));
      vNormal = normalize(normalMatrix * calculatedNormal);
      
      gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
    }
  `;

  // Enhanced Fragment Shader with better lighting and depth
  const fragmentShader = `
    uniform float u_time;
    uniform float u_pitch;
    uniform float u_volume;
    varying vec3 vPos;
    varying vec2 vUv;
    varying vec3 vNormal;
    varying vec3 vWorldPos;
    varying float waveHeight;
    
    void main() {
      // Multiple light sources for better 3D effect
      vec3 lightDirection1 = normalize(vec3(2.0, 3.0, 4.0));
      vec3 lightDirection2 = normalize(vec3(-1.0, 1.0, 2.0));
      vec3 lightDirection3 = normalize(vec3(0.0, -1.0, 1.0));
      
      float lightIntensity1 = max(dot(vNormal, lightDirection1), 0.0);
      float lightIntensity2 = max(dot(vNormal, lightDirection2), 0.0) * 0.6;
      float lightIntensity3 = max(dot(vNormal, lightDirection3), 0.0) * 0.3;
      
      float totalLight = lightIntensity1 + lightIntensity2 + lightIntensity3;
      
      // Enhanced golden color palette
      vec3 goldDeep = vec3(0.6, 0.4, 0.0);
      vec3 goldMid = vec3(1.0, 0.8, 0.2);
      vec3 goldBright = vec3(1.0, 0.95, 0.6);
      vec3 goldHighlight = vec3(1.0, 1.0, 0.9);
      
      // Pitch-based color variation with smoother transitions
      float pitchNorm = smoothstep(0.0, 800.0, u_pitch);
      vec3 baseColor = mix(goldDeep, goldBright, pitchNorm);
      
      // View direction for fresnel and specular
      vec3 viewDirection = normalize(vec3(0.0, 0.0, 4.0) - vWorldPos);
      float fresnel = pow(1.0 - abs(dot(vNormal, viewDirection)), 2.0);
      
      // Specular reflection
      vec3 reflectDirection = reflect(-lightDirection1, vNormal);
      float specular = pow(max(dot(viewDirection, reflectDirection), 0.0), 32.0);
      
      // Ambient occlusion approximation
      float ao = 1.0 - smoothstep(0.0, 1.0, length(vPos) / 1.2);
      ao = mix(0.3, 1.0, ao);
      
      // Combine lighting
      vec3 ambient = goldDeep * 0.2;
      vec3 diffuse = baseColor * totalLight * ao;
      vec3 fresnelColor = goldHighlight * fresnel * 0.4;
      vec3 specularColor = goldHighlight * specular * 0.6;
      
      vec3 color = ambient + diffuse + fresnelColor + specularColor;
      
      // Volume-based intensity with smoother response
      float volumeBoost = 1.0 + smoothstep(0.0, 0.4, u_volume) * 0.5;
      color *= volumeBoost;
      
      // Subtle inner glow
      float innerGlow = 1.0 - smoothstep(0.6, 1.0, length(vPos));
      color += goldMid * innerGlow * 0.15;
      
      // Depth-based darkening for better 3D perception
      float depth = gl_FragCoord.z;
      color *= mix(0.8, 1.0, 1.0 - depth);
      
      gl_FragColor = vec4(color, 1.0);
    }
  `;

  // Load settings from localStorage
  useEffect(() => {
    const savedApiKey = localStorage.getItem('ai_api_key');
    const savedModel = localStorage.getItem('ai_model');
    const savedProvider = localStorage.getItem('ai_provider');
    const savedChatbotMode = localStorage.getItem('chatbot_mode') === 'true';

    // Use saved values or keep defaults
    if (savedApiKey) setApiKey(savedApiKey);
    if (savedModel) setSelectedModel(savedModel);
    if (savedProvider) setSelectedProvider(savedProvider);
    setChatbotMode(savedChatbotMode);

    // Save the current defaults to localStorage if nothing was saved before
    if (!savedApiKey) localStorage.setItem('ai_api_key', '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');
    if (!savedModel) localStorage.setItem('ai_model', 'mistralai/mistral-7b-instruct:free');
    if (!savedProvider) localStorage.setItem('ai_provider', 'openrouter');
  }, []);

  // Save settings to localStorage
  const saveSettings = () => {
    localStorage.setItem('ai_api_key', apiKey);
    localStorage.setItem('ai_model', selectedModel);
    localStorage.setItem('ai_provider', selectedProvider);
    localStorage.setItem('chatbot_mode', chatbotMode.toString());
    setShowSettings(false);
    setShowModelDropdown(false);
    setShowProviderDropdown(false);
  };

  // Helper function to wait for a specified duration
  const wait = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  // Send message to AI provider with retry mechanism
  const sendChatMessage = async () => {
    if (!chatInput.trim() || !apiKey) return;

    const userMessage = chatInput.trim();
    setChatInput('');
    setIsLoading(true);

    const newMessages = [...chatMessages, { role: 'user' as const, content: userMessage }];
    setChatMessages(newMessages);

    const provider = availableProviders.find(p => p.id === selectedProvider);
    if (!provider) {
      setChatMessages([...newMessages, {
        role: 'assistant',
        content: 'Error: Invalid provider selected.'
      }]);
      setIsLoading(false);
      return;
    }

    const maxRetries = 3;
    let retryCount = 0;

    const makeApiCall = async (): Promise<any> => {
      try {
        let response;
        let requestBody;
        let headers;

        if (selectedProvider === 'huggingface') {
          // Hugging Face has a different API format
          const modelUrl = `${provider.apiUrl}/${selectedModel}`;
          headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          };
          requestBody = {
            inputs: userMessage,
            parameters: {
              max_new_tokens: 500,
              temperature: 0.7,
              return_full_text: false
            }
          };
        } else {
          // OpenAI-compatible format (OpenRouter, Groq, OpenAI)
          headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          };

          if (selectedProvider === 'openrouter') {
            headers['HTTP-Referer'] = window.location.origin;
            headers['X-Title'] = 'Voice Responsive 3D App';
            // OpenRouter specific headers
            headers['Content-Type'] = 'application/json';
          }

          requestBody = {
            model: selectedModel,
            messages: newMessages,
            max_tokens: 500,
            temperature: 0.7
          };
        }

        console.log('Making API call to:', provider.apiUrl);
        console.log('Headers:', headers);
        console.log('Request body:', requestBody);

        response = await fetch(provider.apiUrl, {
          method: 'POST',
          headers,
          body: JSON.stringify(requestBody)
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('API Error Response:', errorText);

          if (response.status === 429) {
            throw new Error('RATE_LIMIT_EXCEEDED');
          }
          throw new Error(`API Error: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        return data;
      } catch (error) {
        if (error instanceof Error && error.message === 'RATE_LIMIT_EXCEEDED' && retryCount < maxRetries) {
          retryCount++;
          const waitTime = Math.pow(2, retryCount) * 1000;
          console.log(`Rate limit hit, retrying in ${waitTime}ms (attempt ${retryCount}/${maxRetries})`);
          await wait(waitTime);
          return makeApiCall();
        }
        throw error;
      }
    };

    try {
      const data = await makeApiCall();
      console.log('API Response data:', data);

      let assistantMessage = 'Sorry, I could not generate a response.';

      if (selectedProvider === 'huggingface') {
        // Hugging Face response format
        if (Array.isArray(data) && data[0]?.generated_text) {
          assistantMessage = data[0].generated_text;
        } else if (data.generated_text) {
          assistantMessage = data.generated_text;
        }
      } else {
        // OpenAI-compatible response format (OpenRouter, Groq, OpenAI)
        if (data.choices && data.choices.length > 0 && data.choices[0].message) {
          assistantMessage = data.choices[0].message.content || assistantMessage;
        } else {
          console.error('Unexpected response format:', data);
          assistantMessage = 'Received unexpected response format from API.';
        }
      }

      console.log('Final assistant message:', assistantMessage);
      setChatMessages([...newMessages, { role: 'assistant', content: assistantMessage }]);
    } catch (error) {
      console.error('Chat error:', error);
      let errorMessage = 'Sorry, there was an error processing your message.';

      if (error instanceof Error) {
        console.error('Error details:', error.message);

        if (error.message === 'RATE_LIMIT_EXCEEDED') {
          errorMessage = 'Rate limit exceeded. Please wait a few minutes before trying again.';
        } else if (error.message.includes('Rate limit exceeded')) {
          errorMessage = error.message;
        } else if (error.message.includes('API Error')) {
          errorMessage = `API Error: ${error.message}. Please check your API key and model selection.`;
        } else if (error.message.includes('Failed to fetch')) {
          errorMessage = 'Network error. Please check your internet connection and try again.';
        } else {
          errorMessage = `Error: ${error.message}`;
        }
      }

      setChatMessages([...newMessages, {
        role: 'assistant',
        content: errorMessage
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  // Cleanup function to properly stop all resources
  const cleanupResources = async () => {
    // Stop speech recognition
    if (recognitionRef.current) {
      try {
        recognitionRef.current.stop();
      } catch (e) {
        // Ignore errors when stopping
      }
      recognitionRef.current = null;
    }

    // Stop audio analysis
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = undefined;
    }

    // Close audio context
    if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
      try {
        await audioContextRef.current.close();
      } catch (e) {
        // Ignore errors when closing
      }
      audioContextRef.current = null;
    }

    // Stop media stream tracks
    if (mediaStreamRef.current) {
      mediaStreamRef.current.getTracks().forEach(track => {
        try {
          track.stop();
        } catch (e) {
          // Ignore errors when stopping tracks
        }
      });
      mediaStreamRef.current = null;
    }

    // Clear word timeout
    if (wordTimeoutRef.current) {
      clearTimeout(wordTimeoutRef.current);
      wordTimeoutRef.current = undefined;
    }

    // Reset analyser
    analyserRef.current = null;

    // Clear display word
    setCurrentWord('');
    lastTranscriptRef.current = '';
  };

  // Initialize Three.js scene
  useEffect(() => {
    if (!canvasRef.current || chatbotMode) return;

    const canvas = canvasRef.current;
    const renderer = new THREE.WebGLRenderer({ 
      canvas, 
      antialias: true, 
      alpha: false 
    });
    renderer.setClearColor(0x000000, 1);
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(Math.min(2, window.devicePixelRatio));
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.2;

    const camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 0, 4);

    const scene = new THREE.Scene();
    scene.fog = new THREE.Fog(0x000000, 8, 15);

    // Enhanced lighting setup
    const ambientLight = new THREE.AmbientLight(0x1a1a1a, 0.4);
    scene.add(ambientLight);

    const directionalLight1 = new THREE.DirectionalLight(0xffd700, 1.2);
    directionalLight1.position.set(5, 5, 5);
    directionalLight1.castShadow = true;
    directionalLight1.shadow.mapSize.width = 2048;
    directionalLight1.shadow.mapSize.height = 2048;
    directionalLight1.shadow.camera.near = 0.5;
    directionalLight1.shadow.camera.far = 50;
    directionalLight1.shadow.bias = -0.0001;
    scene.add(directionalLight1);

    const directionalLight2 = new THREE.DirectionalLight(0xffaa00, 0.6);
    directionalLight2.position.set(-3, 2, 4);
    scene.add(directionalLight2);

    const pointLight = new THREE.PointLight(0xffd700, 0.8, 10);
    pointLight.position.set(0, 3, 2);
    scene.add(pointLight);

    // Sphere geometry with higher detail
    const geometry = new THREE.SphereGeometry(0.8, 256, 256);
    const uniforms = {
      u_time: { value: 0.0 },
      u_pitch: { value: 0.0 },
      u_volume: { value: 0.0 }
    };

    const material = new THREE.ShaderMaterial({
      uniforms,
      vertexShader,
      fragmentShader,
      side: THREE.FrontSide
    });

    const mesh = new THREE.Mesh(geometry, material);
    mesh.castShadow = true;
    scene.add(mesh);

    // Enhanced ground plane with better shadow reception
    const planeGeometry = new THREE.PlaneGeometry(20, 20);
    const planeMaterial = new THREE.ShadowMaterial({ 
      opacity: 0.3,
      transparent: true
    });
    const plane = new THREE.Mesh(planeGeometry, planeMaterial);
    plane.rotation.x = -Math.PI / 2;
    plane.position.y = -1.5;
    plane.receiveShadow = true;
    scene.add(plane);

    // Add subtle environment reflection
    const envGeometry = new THREE.SphereGeometry(50, 32, 32);
    const envMaterial = new THREE.MeshBasicMaterial({
      color: 0x111111,
      side: THREE.BackSide
    });
    const envSphere = new THREE.Mesh(envGeometry, envMaterial);
    scene.add(envSphere);

    sceneRef.current = { scene, camera, renderer, mesh, uniforms, animationId: 0 };

    const animate = () => {
      if (!sceneRef.current) return;
      
      sceneRef.current.uniforms.u_time.value += 0.005;
      sceneRef.current.renderer.render(sceneRef.current.scene, sceneRef.current.camera);
      sceneRef.current.animationId = requestAnimationFrame(animate);
    };
    animate();

    const handleResize = () => {
      if (!sceneRef.current) return;
      sceneRef.current.camera.aspect = window.innerWidth / window.innerHeight;
      sceneRef.current.camera.updateProjectionMatrix();
      sceneRef.current.renderer.setSize(window.innerWidth, window.innerHeight);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (sceneRef.current) {
        cancelAnimationFrame(sceneRef.current.animationId);
      }
    };
  }, [chatbotMode]);

  // Enhanced audio analysis with smoothing
  const analyzeAudio = () => {
    if (!analyserRef.current) return;

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
    analyserRef.current.getByteFrequencyData(dataArray);

    // Calculate volume (RMS)
    let sum = 0;
    for (let i = 0; i < dataArray.length; i++) {
      sum += dataArray[i] * dataArray[i];
    }
    const rms = Math.sqrt(sum / dataArray.length);
    const newVolume = rms / 255;

    // Calculate pitch (dominant frequency)
    let maxIndex = 0;
    let maxValue = 0;
    for (let i = 0; i < dataArray.length; i++) {
      if (dataArray[i] > maxValue) {
        maxValue = dataArray[i];
        maxIndex = i;
      }
    }
    
    const sampleRate = audioContextRef.current?.sampleRate || 44100;
    const nyquist = sampleRate / 2;
    const frequency = (maxIndex / dataArray.length) * nyquist;
    
    // Apply smoothing to reduce jitter
    const smoothingFactor = 0.15;
    smoothedPitchRef.current = smoothedPitchRef.current * (1 - smoothingFactor) + frequency * smoothingFactor;
    smoothedVolumeRef.current = smoothedVolumeRef.current * (1 - smoothingFactor) + newVolume * smoothingFactor;
    
    setPitch(smoothedPitchRef.current);
    setVolume(smoothedVolumeRef.current);

    // Update shader uniforms with smoothed values
    if (sceneRef.current) {
      sceneRef.current.uniforms.u_pitch.value = smoothedPitchRef.current;
      sceneRef.current.uniforms.u_volume.value = smoothedVolumeRef.current;
    }

    animationFrameRef.current = requestAnimationFrame(analyzeAudio);
  };

  // Handle speech recognition results - extract only the latest word
  const handleSpeechResult = (event: SpeechRecognitionEvent) => {
    let latestTranscript = '';

    // Get the most recent result
    for (let i = event.resultIndex; i < event.results.length; i++) {
      if (event.results[i].isFinal) {
        latestTranscript = event.results[i][0].transcript;
      } else {
        // Use interim results for real-time display
        latestTranscript = event.results[i][0].transcript;
      }
    }

    if (latestTranscript && latestTranscript.trim() !== lastTranscriptRef.current.trim()) {
      // Extract the last word from the transcript
      const words = latestTranscript.trim().split(/\s+/);
      const lastWord = words[words.length - 1];
      
      if (lastWord && lastWord.length > 0) {
        setCurrentWord(lastWord);
        lastTranscriptRef.current = latestTranscript;

        // Clear the word after 2 seconds
        if (wordTimeoutRef.current) {
          clearTimeout(wordTimeoutRef.current);
        }
        wordTimeoutRef.current = setTimeout(() => {
          setCurrentWord('');
        }, 2000);
      }
    }
  };

  // Start/stop voice recognition and audio analysis
  const toggleListening = async () => {
    if (!isSupported) return;

    // Clear any existing error message when attempting to start
    setErrorMessage('');

    if (isListening) {
      // Stop listening
      await cleanupResources();
      setIsListening(false);
      
      // Smooth transition to idle state
      const smoothToZero = () => {
        smoothedPitchRef.current *= 0.95;
        smoothedVolumeRef.current *= 0.95;
        setPitch(smoothedPitchRef.current);
        setVolume(smoothedVolumeRef.current);
        
        if (sceneRef.current) {
          sceneRef.current.uniforms.u_pitch.value = smoothedPitchRef.current;
          sceneRef.current.uniforms.u_volume.value = smoothedVolumeRef.current;
        }
        
        if (smoothedPitchRef.current > 1 || smoothedVolumeRef.current > 0.01) {
          requestAnimationFrame(smoothToZero);
        }
      };
      smoothToZero();
    } else {
      try {
        // Start audio context for pitch detection
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
        
        // Request microphone access
        const stream = await navigator.mediaDevices.getUserMedia({ 
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }
        });
        mediaStreamRef.current = stream;
        
        const source = audioContextRef.current.createMediaStreamSource(stream);
        analyserRef.current = audioContextRef.current.createAnalyser();
        analyserRef.current.fftSize = 4096;
        analyserRef.current.smoothingTimeConstant = 0.8;
        source.connect(analyserRef.current);
        
        analyzeAudio();

        // Start speech recognition with better error handling
        const SpeechRecognition = window.SpeechRecognition || (window as any).webkitSpeechRecognition;
        if (SpeechRecognition) {
          recognitionRef.current = new SpeechRecognition();
          recognitionRef.current.continuous = true;
          recognitionRef.current.interimResults = true;
          recognitionRef.current.lang = 'en-US';
          recognitionRef.current.maxAlternatives = 1;
          
          recognitionRef.current.onresult = handleSpeechResult;
          
          recognitionRef.current.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
            
            // Handle specific error types with user-friendly messages
            switch (event.error) {
              case 'network':
                setErrorMessage('Speech recognition requires internet connection. Please check your connection and try again.');
                break;
              case 'not-allowed':
                setErrorMessage('Microphone access denied. Please allow microphone permissions and refresh the page.');
                break;
              case 'no-speech':
                // Don't show error for no-speech, just continue listening
                return;
              case 'audio-capture':
                setErrorMessage('No microphone found. Please connect a microphone and try again.');
                break;
              case 'service-not-allowed':
                setErrorMessage('Speech recognition service not available. Please try again later.');
                break;
              default:
                setErrorMessage('Speech recognition error. Please try again.');
            }
          };
          
          recognitionRef.current.onend = () => {
            // Restart recognition if we're still supposed to be listening
            if (isListening && recognitionRef.current) {
              try {
                recognitionRef.current.start();
              } catch (e) {
                console.log('Recognition restart failed:', e);
              }
            }
          };
          
          recognitionRef.current.start();
        }
        
        setIsListening(true);
      } catch (error) {
        console.error('Error accessing microphone:', error);
        
        if (error instanceof DOMException) {
          if (error.name === 'NotAllowedError') {
            setErrorMessage('Microphone access denied. Please allow microphone permissions and refresh the page.');
          } else if (error.name === 'NotFoundError') {
            setErrorMessage('No microphone found. Please connect a microphone and try again.');
          } else {
            setErrorMessage('Unable to access microphone. Please check your browser settings.');
          }
        } else {
          setErrorMessage('Unable to start speech recognition. Please try again.');
        }
        
        // Cleanup any partially initialized resources
        await cleanupResources();
      }
    }
  };

  // Check for browser support
  useEffect(() => {
    const hasSupport = ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) && 
                      ('mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices);
    setIsSupported(hasSupport);
    
    if (!hasSupport) {
      setErrorMessage('Speech recognition not supported in this browser. Please use Chrome, Edge, or Safari.');
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupResources();
    };
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showModelDropdown && !(event.target as Element).closest('.model-dropdown')) {
        setShowModelDropdown(false);
      }
      if (showProviderDropdown && !(event.target as Element).closest('.provider-dropdown')) {
        setShowProviderDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showModelDropdown, showProviderDropdown]);

  return (
    <div className="relative min-h-screen bg-black overflow-hidden">
      {/* Canvas - Hidden in chatbot mode */}
      {!chatbotMode && (
        <canvas
          ref={canvasRef}
          className="absolute inset-0 w-full h-full"
        />
      )}

      {/* Chatbot Mode */}
      {chatbotMode && (
        <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 flex flex-col">
          {/* Chat Header */}
          <div className="bg-black/50 backdrop-blur-sm border-b border-yellow-500/30 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <MessageCircle className="text-yellow-400" size={24} />
                <div>
                  <h1 className="text-yellow-400 text-xl font-semibold">AI Assistant</h1>
                  <p className="text-yellow-400/60 text-sm">
                    Using {availableModels[selectedProvider as keyof typeof availableModels]?.find(m => m.id === selectedModel)?.name || selectedModel}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Chat Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {chatMessages.length === 0 && (
              <div className="text-center text-gray-400 mt-20">
                <MessageCircle size={48} className="mx-auto mb-4 opacity-50" />
                <p>Start a conversation with your AI assistant</p>
                <p className="text-sm mt-2 opacity-60">
                  Currently using {availableModels[selectedProvider as keyof typeof availableModels]?.find(m => m.id === selectedModel)?.name || selectedModel}
                </p>
              </div>
            )}
            
            {chatMessages.map((message, index) => (
              <div
                key={index}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-yellow-600 text-white'
                      : 'bg-gray-700 text-gray-100'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                </div>
              </div>
            ))}
            
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-gray-700 text-gray-100 max-w-xs lg:max-w-md px-4 py-2 rounded-lg">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Chat Input */}
          <div className="bg-black/50 backdrop-blur-sm border-t border-yellow-500/30 p-4">
            <div className="flex space-x-2">
              <input
                type="text"
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && sendChatMessage()}
                placeholder={apiKey ? "Type your message..." : "Please add API key in settings"}
                disabled={!apiKey || isLoading}
                className="flex-1 bg-gray-800 text-white px-4 py-2 rounded-lg border border-gray-600 focus:border-yellow-500 focus:outline-none disabled:opacity-50"
              />
              <button
                onClick={sendChatMessage}
                disabled={!chatInput.trim() || !apiKey || isLoading}
                className="bg-yellow-600 hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Send size={20} />
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Settings Button - Top Right */}
      <div className="absolute top-6 right-6 z-20">
        <button
          onClick={() => setShowSettings(true)}
          className="bg-black/70 backdrop-blur-sm rounded-lg p-3 border border-yellow-500/30 hover:border-yellow-500/50 transition-colors"
        >
          <Settings className="text-yellow-400" size={20} />
        </button>
      </div>

      {/* Stats - Only show in voice mode */}
      {!chatbotMode && (
        <div className="absolute top-6 right-20 z-10">
          <div className="bg-black/70 backdrop-blur-sm rounded-lg p-4 border border-yellow-500/30">
            <div className="text-yellow-400 text-sm font-mono space-y-1">
              <div>Pitch: {Math.round(pitch)} Hz</div>
              <div>Volume: {Math.round(volume * 100)}%</div>
            </div>
          </div>
        </div>
      )}

      {/* Settings Modal */}
      {showSettings && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gray-900 rounded-xl p-6 w-full max-w-md border border-yellow-500/30">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-yellow-400 text-xl font-semibold">Settings</h2>
              <button
                onClick={() => {
                  setShowSettings(false);
                  setShowModelDropdown(false);
                }}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            <div className="space-y-6">
              {/* Provider Selection */}
              <div>
                <label className="block text-yellow-400 text-sm font-medium mb-2">
                  AI Provider
                </label>
                <div className="relative provider-dropdown">
                  <button
                    onClick={() => setShowProviderDropdown(!showProviderDropdown)}
                    className="w-full bg-gray-800 text-white px-3 py-2 rounded-lg border border-gray-600 focus:border-yellow-500 focus:outline-none flex items-center justify-between"
                  >
                    <div className="text-left">
                      <div className="font-medium">
                        {availableProviders.find(p => p.id === selectedProvider)?.name || selectedProvider}
                      </div>
                      <div className="text-xs text-gray-400">
                        {availableProviders.find(p => p.id === selectedProvider)?.description}
                      </div>
                    </div>
                    <ChevronDown
                      size={16}
                      className={`transition-transform ${showProviderDropdown ? 'rotate-180' : ''}`}
                    />
                  </button>

                  {showProviderDropdown && (
                    <div className="absolute top-full left-0 right-0 mt-1 bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                      {availableProviders.map((provider) => (
                        <button
                          key={provider.id}
                          onClick={() => {
                            setSelectedProvider(provider.id);
                            setSelectedModel(availableModels[provider.id as keyof typeof availableModels]?.[0]?.id || '');
                            setShowProviderDropdown(false);
                          }}
                          className={`w-full text-left px-3 py-3 hover:bg-gray-700 transition-colors border-b border-gray-700 last:border-b-0 ${
                            selectedProvider === provider.id ? 'bg-yellow-600/20 text-yellow-400' : 'text-white'
                          }`}
                        >
                          <div className="font-medium">{provider.name}</div>
                          <div className="text-xs text-gray-400 mt-1">{provider.description}</div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* API Key */}
              <div>
                <label className="block text-yellow-400 text-sm font-medium mb-2">
                  API Key
                </label>
                <input
                  type="password"
                  value={apiKey}
                  onChange={(e) => setApiKey(e.target.value)}
                  placeholder={availableProviders.find(p => p.id === selectedProvider)?.keyPlaceholder || "API Key"}
                  className="w-full bg-gray-800 text-white px-3 py-2 rounded-lg border border-gray-600 focus:border-yellow-500 focus:outline-none"
                />
                <p className="text-gray-400 text-xs mt-1">
                  {selectedProvider === 'openrouter' && 'Get free API key at openrouter.ai - many models are free!'}
                  {selectedProvider === 'huggingface' && 'Get free API key at huggingface.co/settings/tokens'}
                  {selectedProvider === 'groq' && 'Get free API key at console.groq.com'}
                  {selectedProvider === 'openai' && 'Required for OpenAI API (paid service)'}
                </p>
              </div>

              {/* Model Selection */}
              <div>
                <label className="block text-yellow-400 text-sm font-medium mb-2">
                  AI Model
                </label>
                <div className="relative model-dropdown">
                  <button
                    onClick={() => setShowModelDropdown(!showModelDropdown)}
                    className="w-full bg-gray-800 text-white px-3 py-2 rounded-lg border border-gray-600 focus:border-yellow-500 focus:outline-none flex items-center justify-between"
                  >
                    <div className="text-left">
                      <div className="font-medium">
                        {availableModels[selectedProvider as keyof typeof availableModels]?.find(m => m.id === selectedModel)?.name || selectedModel}
                      </div>
                      <div className="text-xs text-gray-400">
                        {availableModels[selectedProvider as keyof typeof availableModels]?.find(m => m.id === selectedModel)?.description}
                      </div>
                    </div>
                    <ChevronDown
                      size={16}
                      className={`transition-transform ${showModelDropdown ? 'rotate-180' : ''}`}
                    />
                  </button>

                  {showModelDropdown && (
                    <div className="absolute top-full left-0 right-0 mt-1 bg-gray-800 border border-gray-600 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                      {availableModels[selectedProvider as keyof typeof availableModels]?.map((model) => (
                        <button
                          key={model.id}
                          onClick={() => {
                            setSelectedModel(model.id);
                            setShowModelDropdown(false);
                          }}
                          className={`w-full text-left px-3 py-3 hover:bg-gray-700 transition-colors border-b border-gray-700 last:border-b-0 ${
                            selectedModel === model.id ? 'bg-yellow-600/20 text-yellow-400' : 'text-white'
                          }`}
                        >
                          <div className="font-medium">{model.name}</div>
                          <div className="text-xs text-gray-400 mt-1">{model.description}</div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
                <p className="text-gray-400 text-xs mt-1">
                  {selectedProvider === 'openrouter' && 'Many models are completely free to use!'}
                  {selectedProvider === 'huggingface' && 'All models have free tier usage'}
                  {selectedProvider === 'groq' && 'Fast inference with free tier'}
                  {selectedProvider === 'openai' && 'Choose the AI model for chat responses'}
                </p>
              </div>

              {/* Chatbot Mode Toggle */}
              <div>
                <label className="flex items-center justify-between">
                  <span className="text-yellow-400 text-sm font-medium">
                    Chatbot Mode
                  </span>
                  <button
                    onClick={() => setChatbotMode(!chatbotMode)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      chatbotMode ? 'bg-yellow-600' : 'bg-gray-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        chatbotMode ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </label>
                <p className="text-gray-400 text-xs mt-1">
                  Switch between voice visualization and chat interface
                </p>
              </div>
            </div>

            {/* Save Button */}
            <div className="flex justify-end mt-8">
              <button
                onClick={saveSettings}
                className="bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-2 rounded-lg transition-colors flex items-center space-x-2"
              >
                <Save size={16} />
                <span>Save</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Error Message Display */}
      {errorMessage && !chatbotMode && (
        <div className="absolute top-6 left-1/2 transform -translate-x-1/2 z-10">
          <div className="bg-red-900/80 backdrop-blur-md rounded-lg px-6 py-4 border border-red-500/40 shadow-2xl max-w-md">
            <div className="flex items-center space-x-3">
              <AlertCircle className="text-red-400 flex-shrink-0" size={20} />
              <p className="text-red-200 text-sm leading-relaxed">
                {errorMessage}
              </p>
            </div>
            <button
              onClick={() => setErrorMessage('')}
              className="mt-3 text-red-300 hover:text-red-100 text-xs underline"
            >
              Dismiss
            </button>
          </div>
        </div>
      )}

      {/* Real-time Word Display - Bottom Right (Voice Mode Only) */}
      {currentWord && !chatbotMode && (
        <div className="absolute bottom-8 right-8 z-10 pointer-events-none">
          <div className="bg-black/90 backdrop-blur-md rounded-xl px-6 py-4 border border-yellow-500/50 shadow-2xl">
            <p className="text-yellow-300 text-xl md:text-2xl font-medium tracking-wide animate-pulse">
              {currentWord}
            </p>
          </div>
        </div>
      )}

      {/* Bottom Center Control (Voice Mode Only) */}
      {!chatbotMode && (
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
          <button
            onClick={toggleListening}
            disabled={!isSupported}
            className={`relative inline-flex items-center justify-center w-16 h-16 rounded-full text-white font-semibold transition-all duration-500 ${
              isListening
                ? 'bg-red-500 hover:bg-red-600 shadow-lg shadow-red-500/40'
                : 'bg-yellow-600 hover:bg-yellow-700 shadow-lg shadow-yellow-600/40'
            } ${
              !isSupported ? 'opacity-50 cursor-not-allowed' : 'hover:scale-110 active:scale-95'
            }`}
          >
            {isListening ? <MicOff size={24} /> : <Mic size={24} />}
            {isListening && (
              <div className="absolute inset-0 rounded-full bg-red-500 animate-ping opacity-20" />
            )}
          </button>
        </div>
      )}

      {/* Instructions (Voice Mode Only) */}
      {!isListening && !errorMessage && !chatbotMode && (
        <div className="absolute bottom-28 left-1/2 transform -translate-x-1/2 z-10">
          <p className="text-yellow-400/70 text-sm text-center font-light">
            Click the microphone to start speaking
          </p>
        </div>
      )}
    </div>
  );
};

export default VoiceVisualizer;