import React, { useEffect, useRef, useState } from 'react';
import <PERSON>yd<PERSON> from 'meyda';
// import { detectPitch } from 'pitchy'; // Uncomment if using pitchy

export default function SpeechToText({ listening, onPitch, onSpeaking, onTranscript }) {
  const [transcript, setTranscript] = useState('');
  const recognitionRef = useRef(null);
  const audioContextRef = useRef(null);
  const sourceRef = useRef(null);
  const analyzerRef = useRef(null);

  useEffect(() => {
    if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) return;
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();
    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'en-US';
    recognition.onresult = (event) => {
      let finalTranscript = '';
      for (let i = event.resultIndex; i < event.results.length; ++i) {
        finalTranscript += event.results[i][0].transcript;
      }
      setTranscript(finalTranscript);
      if (onTranscript) onTranscript(finalTranscript);
    };
    recognitionRef.current = recognition;
  }, [onTranscript]);

  useEffect(() => {
    if (!listening) return;
    recognitionRef.current.start();
    onSpeaking && onSpeaking(true);
    return () => {
      recognitionRef.current.stop();
      onSpeaking && onSpeaking(false);
    };
  }, [listening, onSpeaking]);

  // RMS detection (use for blob animation)
  useEffect(() => {
    async function setupAudio() {
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      sourceRef.current = audioContextRef.current.createMediaStreamSource(stream);
      analyzerRef.current = Meyda.createMeydaAnalyzer({
        audioContext: audioContextRef.current,
        source: sourceRef.current,
        bufferSize: 512,
        featureExtractors: ['rms'],
        callback: (features) => {
          if (features && features.rms && onPitch) {
            onPitch(features.rms); // Pass raw RMS (0-1)
          }
        },
      });
      analyzerRef.current.start();
    }
    if (listening) {
      setupAudio();
    }
    return () => {
      if (analyzerRef.current) analyzerRef.current.stop();
      if (audioContextRef.current) audioContextRef.current.close();
    };
  }, [listening, onPitch]);

  // Only show transcript, styled for black background
  return (
    <div style={{ color: '#fff', fontSize: 28, marginTop: 24, minHeight: 40, textAlign: 'center', letterSpacing: '0.5px', fontWeight: 400 }}>
      {transcript || ''}
    </div>
  );
} 