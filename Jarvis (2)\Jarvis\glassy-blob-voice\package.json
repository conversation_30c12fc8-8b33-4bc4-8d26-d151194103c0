{"name": "glassy-blob-voice", "version": "0.1.0", "private": true, "dependencies": {"@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "cra-template-pwa": "2.0.0", "meyda": "^5.6.3", "pitchy": "^4.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "simplex-noise": "^4.0.3", "three": "^0.177.0", "web-vitals": "^5.0.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}