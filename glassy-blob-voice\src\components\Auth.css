@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&display=swap');

.auth-container {
  min-height: 100vh;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.auth-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  animation: pulse 4s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% { opacity: 0.5; }
  100% { opacity: 1; }
}

.auth-form {
  background: #000;
  border: 2.5px solid #FFD700;
  border-radius: 10px;
  padding: 40px;
  width: 100%;
  max-width: 400px;
  position: relative;
  z-index: 10;
  box-shadow: 0 0 32px 4px #FFD70044, 0 8px 32px #000a;
  font-family: 'Orbitron', 'Arial', sans-serif;
}

.auth-form::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 22px;
  z-index: -1;
  opacity: 0.3;
  animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

.auth-title {
  color: #FFD700;
  font-size: 2.2rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 30px;
  letter-spacing: 2px;
  text-shadow: 0 0 12px #FFD70088, 0 2px 16px #000a;
  font-family: 'Orbitron', 'Arial', sans-serif;
}

.auth-subtitle {
  color: #b8b8b8;
  text-align: center;
  margin-bottom: 30px;
  font-size: 1rem;
  font-family: 'Arial', sans-serif;
}

.input-group {
  margin-bottom: 25px;
  position: relative;
}

.input-group label {
  display: block;
  color: #FFD700;
  margin-bottom: 8px;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-family: 'Orbitron', 'Arial', sans-serif;
}

.input-group input {
  width: 100%;
  padding: 15px 20px;
  background: #000;
  border: 2px solid #FFD70044;
  border-radius: 6px;
  color: #FFD700;
  font-size: 1rem;
  font-family: 'Orbitron', 'Arial', sans-serif;
  transition: all 0.3s cubic-bezier(.4,2,.6,1);
  box-sizing: border-box;
  outline: none;
  box-shadow: 0 0 0 0 #FFD700;
}

.input-group input:focus {
  border-color: #FFD700;
  background: #111;
  box-shadow: 0 0 8px 2px #FFD70088;
}

.input-group input::placeholder {
  color: #FFD70099;
  font-family: 'Arial', sans-serif;
}

.auth-button {
  width: 100%;
  padding: 15px;
  background: #FFD700;
  border: none;
  border-radius: 8px;
  color: #000;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: box-shadow 0.3s, background 0.2s, color 0.2s;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 20px;
  box-shadow: 0 0 16px 2px #FFD70088, 0 2px 12px #0006;
  font-family: 'Orbitron', 'Arial', sans-serif;
}

.auth-button:hover {
  background: #fffbe6;
  color: #FFD700;
  box-shadow: 0 0 24px 4px #FFD700cc;
}

.auth-button:active {
  transform: translateY(0);
}

.auth-switch {
  text-align: center;
  color: #b8b8b8;
  font-size: 0.9rem;
  font-family: 'Arial', sans-serif;
}

.auth-switch button {
  background: none;
  border: none;
  color: #FFD700;
  cursor: pointer;
  font-weight: 700;
  text-decoration: underline;
  margin-left: 5px;
  transition: color 0.2s;
  font-family: 'Orbitron', 'Arial', sans-serif;
}

.auth-switch button:hover {
  color: #fffbe6;
}

.error-message {
  background: #1a0000;
  border: 1px solid #FFD70044;
  color: #FFD700;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
  font-size: 0.9rem;
  font-family: 'Arial', sans-serif;
}

.success-message {
  background: #111100;
  border: 1px solid #FFD70044;
  color: #FFD700;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
  font-size: 0.9rem;
  font-family: 'Arial', sans-serif;
}

.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: #fff;
  border-radius: 50%;
  animation: float 6s infinite linear;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

.particle:nth-child(1) { left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { left: 20%; animation-delay: 1s; }
.particle:nth-child(3) { left: 30%; animation-delay: 2s; }
.particle:nth-child(4) { left: 40%; animation-delay: 3s; }
.particle:nth-child(5) { left: 50%; animation-delay: 4s; }
.particle:nth-child(6) { left: 60%; animation-delay: 5s; }
.particle:nth-child(7) { left: 70%; animation-delay: 0.5s; }
.particle:nth-child(8) { left: 80%; animation-delay: 1.5s; }
.particle:nth-child(9) { left: 90%; animation-delay: 2.5s; }

/* Demo credentials box */
.demo-credentials {
  margin-top: 20px;
  padding: 15px;
  background: #000;
  border: 1.5px solid #FFD70044;
  border-radius: 8px;
  font-size: 0.95rem;
  color: #FFD700;
  text-align: center;
  font-weight: 600;
  letter-spacing: 1px;
  font-family: 'Orbitron', 'Arial', sans-serif;
} 