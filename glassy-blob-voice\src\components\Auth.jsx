import React, { useState, useEffect } from 'react';
import './Auth.css';

const Auth = ({ onAuthSuccess }) => {
  const [isLogin, setIsLogin] = useState(true);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    confirmPassword: ''
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Default credentials
  const DEFAULT_USERNAME = 'admin';
  const DEFAULT_PASSWORD = 'password';

  useEffect(() => {
    // Auto-fill default credentials for demo
    if (isLogin) {
      setFormData({
        username: DEFAULT_USERNAME,
        password: DEFAULT_PASSWORD,
        confirmPassword: ''
      });
    }
  }, [isLogin]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError('');
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (isLogin) {
      // Login logic
      if (formData.username === DEFAULT_USERNAME && formData.password === DEFAULT_PASSWORD) {
        setSuccess('Login successful! Welcome back.');
        setTimeout(() => {
          onAuthSuccess(formData.username);
        }, 1000);
      } else {
        setError('Invalid username or password. Try admin/password');
      }
    } else {
      // Signup logic
      if (!formData.username || !formData.password) {
        setError('Please fill in all fields');
        return;
      }
      if (formData.password !== formData.confirmPassword) {
        setError('Passwords do not match');
        return;
      }
      if (formData.password.length < 6) {
        setError('Password must be at least 6 characters long');
        return;
      }
      
      setSuccess('Account created successfully! You can now login.');
      setTimeout(() => {
        setIsLogin(true);
        setFormData({
          username: formData.username,
          password: formData.password,
          confirmPassword: ''
        });
      }, 1500);
    }
  };

  const toggleMode = () => {
    setIsLogin(!isLogin);
    setFormData({
      username: '',
      password: '',
      confirmPassword: ''
    });
    setError('');
    setSuccess('');
  };

  return (
    <div className="auth-container">
      <form className="auth-form" onSubmit={handleSubmit}>
        <h1 className="auth-title">
          {isLogin ? 'LOGIN' : 'SIGN UP'}
        </h1>
        <p className="auth-subtitle">
          {isLogin 
            ? 'Welcome back to the future' 
            : 'Join the digital revolution'
          }
        </p>

        {error && <div className="error-message">{error}</div>}
        {success && <div className="success-message">{success}</div>}

        <div className="input-group">
          <label htmlFor="username">Username</label>
          <input
            type="text"
            id="username"
            name="username"
            value={formData.username}
            onChange={handleInputChange}
            placeholder="Enter your username"
            required
          />
        </div>

        <div className="input-group">
          <label htmlFor="password">Password</label>
          <input
            type="password"
            id="password"
            name="password"
            value={formData.password}
            onChange={handleInputChange}
            placeholder="Enter your password"
            required
          />
        </div>

        {!isLogin && (
          <div className="input-group">
            <label htmlFor="confirmPassword">Confirm Password</label>
            <input
              type="password"
              id="confirmPassword"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              placeholder="Confirm your password"
              required
            />
          </div>
        )}

        <button type="submit" className="auth-button">
          {isLogin ? 'ACCESS SYSTEM' : 'CREATE ACCOUNT'}
        </button>

        <div className="auth-switch">
          {isLogin ? "Don't have an account?" : "Already have an account?"}
          <button type="button" onClick={toggleMode}>
            {isLogin ? 'Sign Up' : 'Login'}
          </button>
        </div>

        {isLogin && (
          <div className="demo-credentials">
            Demo Credentials: <strong>admin</strong> / <strong>password</strong>
          </div>
        )}
      </form>
    </div>
  );
};

export default Auth; 