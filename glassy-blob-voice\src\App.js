import React, { useState, useRef } from 'react';
import './App.css';
import Glacisphere from './components/GlassyBlob';
import SpeechToText from './components/SpeechToText';

function App() {
  const [intensity, setIntensity] = useState(0.8); // Higher default for more lively animation
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [transcript, setTranscript] = useState('');
  const synthRef = useRef(window.speechSynthesis);

  // Determine mode for blob animation
  let mode = 'idle';
  if (isListening) mode = 'listening';
  if (isSpeaking) mode = 'speaking';

  // Animation params for the blob
  let blobProps = { intensity };
  if (mode === 'listening') {
    blobProps = { ...blobProps, speed: 0.008, ampBase: 0.22, ampVar: 0.32 };
  } else if (mode === 'speaking') {
    blobProps = { ...blobProps, speed: 0.055, ampBase: 0.18, ampVar: 0.28 };
  } else {
    blobProps = { ...blobProps, speed: 0.012, ampBase: 0.12, ampVar: 0.18 };
  }

  const handleSpeak = () => {
    if (!transcript) return;
    if (!('speechSynthesis' in window)) return;
    const utter = new window.SpeechSynthesisUtterance(transcript);
    utter.onstart = () => setIsSpeaking(true);
    utter.onend = () => setIsSpeaking(false);
    synthRef.current.speak(utter);
  };

  return (
    <div className="App" style={{ minHeight: '100vh', background: '#000', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', position: 'relative' }}>
      {/* Transcript floats above the blob */}
      <div className="transcript-float" style={{
        position: 'absolute',
        top: '12%',
        left: '50%',
        transform: 'translateX(-50%)',
        color: '#fff',
        fontSize: 34,
        fontWeight: 500,
        textAlign: 'center',
        minHeight: 40,
        letterSpacing: '0.5px',
        maxWidth: 600,
        zIndex: 2,
        textShadow: '0 2px 24px #000a',
        opacity: transcript ? 1 : 0.5,
        transition: 'opacity 0.5s',
        pointerEvents: 'none',
      }}>
        {transcript || <span style={{ color: '#555' }}>Say something…</span>}
      </div>
      {/* Blob with shadow */}
      <div style={{ position: 'relative', zIndex: 1 }}>
        <Glacisphere mode={mode} {...blobProps} />
        <div style={{
          position: 'absolute',
          left: '50%',
          bottom: 30,
          width: 220,
          height: 40,
          background: 'radial-gradient(ellipse at center, #0008 0%, #0000 80%)',
          transform: 'translateX(-50%)',
          borderRadius: '50%',
          filter: 'blur(8px)',
          zIndex: 0,
        }} />
      </div>
      {/* Controls */}
      <div style={{ display: 'flex', gap: 24, marginTop: 32, zIndex: 3 }}>
        <button
          className="main-btn"
          onClick={() => setIsListening(l => !l)}
          style={{ fontSize: 22, fontWeight: 700, padding: '0.8em 2.2em', borderRadius: 18 }}
        >
          {isListening ? 'Stop Listening' : 'Start Listening'}
        </button>
        <button
          className="main-btn"
          onClick={handleSpeak}
          style={{ fontSize: 22, fontWeight: 700, padding: '0.8em 2.2em', borderRadius: 18, background: '#FFD700', color: '#222' }}
          disabled={!transcript}
        >
          Speak
        </button>
      </div>
      {/* Hidden SpeechToText for real-time transcript and intensity */}
      <SpeechToText
        listening={isListening}
        onPitch={rms => setIntensity(Math.max(0.1, Math.min(1, rms)))}
        onSpeaking={setIsSpeaking}
        onTranscript={setTranscript}
      />
    </div>
  );
}

export default App;
