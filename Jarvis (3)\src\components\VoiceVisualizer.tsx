import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
import { Mic, MicOff } from 'lucide-react';

const VoiceVisualizer: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const sceneRef = useRef<{
    scene: THREE.Scene;
    camera: THREE.PerspectiveCamera;
    renderer: THREE.WebGLRenderer;
    mesh: THREE.Mesh;
    uniforms: any;
    animationId: number;
  } | null>(null);
  
  const [isListening, setIsListening] = useState(false);
  const [pitch, setPitch] = useState(0);
  const [volume, setVolume] = useState(0);
  const [isSupported, setIsSupported] = useState(true);
  
  // Smoothing variables
  const smoothedPitchRef = useRef(0);
  const smoothedVolumeRef = useRef(0);
  
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const animationFrameRef = useRef<number>();

  // Enhanced Vertex Shader with smoother animations
  const vertexShader = `
    varying vec2 vUv;
    varying vec3 vPos;
    varying vec3 vNormal;
    varying vec3 vWorldPos;
    varying float waveHeight;
    uniform float u_time;
    uniform float u_pitch;
    uniform float u_volume;
    
    vec4 permute(vec4 x){return mod(((x*34.0)+1.0)*x, 289.0);}
    float permute(float x){return floor(mod(((x*34.0)+1.0)*x, 289.0));}
    vec4 taylorInvSqrt(vec4 r){return 1.79284291400159 - 0.85373472095314 * r;}
    float taylorInvSqrt(float r){return 1.79284291400159 - 0.85373472095314 * r;}

    vec4 grad4(float j, vec4 ip){
      const vec4 ones = vec4(1.0, 1.0, 1.0, -1.0);
      vec4 p,s;
      p.xyz = floor( fract (vec3(j) * ip.xyz) * 7.0) * ip.z - 1.0;
      p.w = 1.5 - dot(abs(p.xyz), ones.xyz);
      s = vec4(lessThan(p, vec4(0.0)));
      p.xyz = p.xyz + (s.xyz*2.0 - 1.0) * s.www; 
      return p;
    }

    float snoise(vec4 v){
      const vec2  C = vec2( 0.138196601125010504, 0.309016994374947451);
      vec4 i  = floor(v + dot(v, C.yyyy) );
      vec4 x0 = v -   i + dot(i, C.xxxx);
      vec4 i0;
      vec3 isX = step( x0.yzw, x0.xxx );
      vec3 isYZ = step( x0.zww, x0.yyz );
      i0.x = isX.x + isX.y + isX.z;
      i0.yzw = 1.0 - isX;
      i0.y += isYZ.x + isYZ.y;
      i0.zw += 1.0 - isYZ.xy;
      i0.z += isYZ.z;
      i0.w += 1.0 - isYZ.z;
      vec4 i3 = clamp( i0, 0.0, 1.0 );
      vec4 i2 = clamp( i0-1.0, 0.0, 1.0 );
      vec4 i1 = clamp( i0-2.0, 0.0, 1.0 );
      vec4 x1 = x0 - i1 + 1.0 * C.xxxx;
      vec4 x2 = x0 - i2 + 2.0 * C.xxxx;
      vec4 x3 = x0 - i3 + 3.0 * C.xxxx;
      vec4 x4 = x0 - 1.0 + 4.0 * C.xxxx;
      i = mod(i, 289.0); 
      float j0 = permute( permute( permute( permute(i.w) + i.z) + i.y) + i.x);
      vec4 j1 = permute( permute( permute( permute (
                   i.w + vec4(i1.w, i2.w, i3.w, 1.0 ))
               + i.z + vec4(i1.z, i2.z, i3.z, 1.0 ))
               + i.y + vec4(i1.y, i2.y, i3.y, 1.0 ))
               + i.x + vec4(i1.x, i2.x, i3.x, 1.0 ));
      vec4 ip = vec4(1.0/294.0, 1.0/49.0, 1.0/7.0, 0.0) ;
      vec4 p0 = grad4(j0,   ip);
      vec4 p1 = grad4(j1.x, ip);
      vec4 p2 = grad4(j1.y, ip);
      vec4 p3 = grad4(j1.z, ip);
      vec4 p4 = grad4(j1.w, ip);
      vec4 norm = taylorInvSqrt(vec4(dot(p0,p0), dot(p1,p1), dot(p2, p2), dot(p3,p3)));
      p0 *= norm.x;
      p1 *= norm.y;
      p2 *= norm.z;
      p3 *= norm.w;
      p4 *= taylorInvSqrt(dot(p4,p4));
      vec3 m0 = max(0.6 - vec3(dot(x0,x0), dot(x1,x1), dot(x2,x2)), 0.0);
      vec2 m1 = max(0.6 - vec2(dot(x3,x3), dot(x4,x4)            ), 0.0);
      m0 = m0 * m0;
      m1 = m1 * m1;
      return 49.0 * ( dot(m0*m0, vec3( dot( p0, x0 ), dot( p1, x1 ), dot( p2, x2 )))
                   + dot(m1*m1, vec2( dot( p3, x3 ), dot( p4, x4 ) ) ) ) ;
    }
    
    float fbm (in vec4 v) {
      float value = 0.0;
      float amplitude = .5;
      for (int i = 0; i < 3; i++) {
        value += amplitude * snoise(v);
        v *= 2.;
        amplitude *= .5;
      }
      return value;
    }
    
    void main() {
      vUv = uv;
      
      // Smoother pitch and volume influence
      float pitchInfluence = smoothstep(0.0, 500.0, u_pitch) * 0.5 + 0.8;
      float volumeInfluence = smoothstep(0.0, 0.3, u_volume) * 0.8 + 0.2;
      
      waveHeight = 0.25 * volumeInfluence;
      float spikes = 0.8 * pitchInfluence;
      float freq = 1.8 + u_pitch * 0.003;
      
      // Slower time progression for smoother animation
      float slowTime = u_time * 0.3;
      
      // Multi-layered noise for more organic movement
      float n1 = fbm(vec4(position.x * spikes, position.y * spikes, position.z * spikes, slowTime));
      float n2 = fbm(vec4(position.x * spikes * 0.5, position.y * spikes * 0.5, position.z * spikes * 0.5, slowTime * 1.5));
      float n = mix(n1, n2, 0.4);
      
      float scalePos = sin(n * freq) * (waveHeight * 0.3) + (1.0 + waveHeight * 0.2);
      vec3 pos = position * scalePos;
      
      vPos = pos;
      vWorldPos = (modelMatrix * vec4(pos, 1.0)).xyz;
      
      // Calculate smooth normals for better lighting
      vec3 tangent = normalize(cross(normal, vec3(0.0, 1.0, 0.0)));
      vec3 bitangent = normalize(cross(normal, tangent));
      float delta = 0.01;
      
      vec3 neighbor1 = position + tangent * delta;
      vec3 neighbor2 = position + bitangent * delta;
      
      float n1_offset = fbm(vec4(neighbor1.x * spikes, neighbor1.y * spikes, neighbor1.z * spikes, slowTime));
      float n2_offset = fbm(vec4(neighbor2.x * spikes, neighbor2.y * spikes, neighbor2.z * spikes, slowTime));
      
      vec3 pos1 = neighbor1 * (sin(n1_offset * freq) * (waveHeight * 0.3) + (1.0 + waveHeight * 0.2));
      vec3 pos2 = neighbor2 * (sin(n2_offset * freq) * (waveHeight * 0.3) + (1.0 + waveHeight * 0.2));
      
      vec3 calculatedNormal = normalize(cross(pos1 - pos, pos2 - pos));
      vNormal = normalize(normalMatrix * calculatedNormal);
      
      gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
    }
  `;

  // Enhanced Fragment Shader with better lighting and depth
  const fragmentShader = `
    uniform float u_time;
    uniform float u_pitch;
    uniform float u_volume;
    varying vec3 vPos;
    varying vec2 vUv;
    varying vec3 vNormal;
    varying vec3 vWorldPos;
    varying float waveHeight;
    
    void main() {
      // Multiple light sources for better 3D effect
      vec3 lightDirection1 = normalize(vec3(2.0, 3.0, 4.0));
      vec3 lightDirection2 = normalize(vec3(-1.0, 1.0, 2.0));
      vec3 lightDirection3 = normalize(vec3(0.0, -1.0, 1.0));
      
      float lightIntensity1 = max(dot(vNormal, lightDirection1), 0.0);
      float lightIntensity2 = max(dot(vNormal, lightDirection2), 0.0) * 0.6;
      float lightIntensity3 = max(dot(vNormal, lightDirection3), 0.0) * 0.3;
      
      float totalLight = lightIntensity1 + lightIntensity2 + lightIntensity3;
      
      // Enhanced golden color palette
      vec3 goldDeep = vec3(0.6, 0.4, 0.0);
      vec3 goldMid = vec3(1.0, 0.8, 0.2);
      vec3 goldBright = vec3(1.0, 0.95, 0.6);
      vec3 goldHighlight = vec3(1.0, 1.0, 0.9);
      
      // Pitch-based color variation with smoother transitions
      float pitchNorm = smoothstep(0.0, 800.0, u_pitch);
      vec3 baseColor = mix(goldDeep, goldBright, pitchNorm);
      
      // View direction for fresnel and specular
      vec3 viewDirection = normalize(cameraPosition - vWorldPos);
      float fresnel = pow(1.0 - abs(dot(vNormal, viewDirection)), 2.0);
      
      // Specular reflection
      vec3 reflectDirection = reflect(-lightDirection1, vNormal);
      float specular = pow(max(dot(viewDirection, reflectDirection), 0.0), 32.0);
      
      // Ambient occlusion approximation
      float ao = 1.0 - smoothstep(0.0, 1.0, length(vPos) / 1.2);
      ao = mix(0.3, 1.0, ao);
      
      // Combine lighting
      vec3 ambient = goldDeep * 0.2;
      vec3 diffuse = baseColor * totalLight * ao;
      vec3 fresnelColor = goldHighlight * fresnel * 0.4;
      vec3 specularColor = goldHighlight * specular * 0.6;
      
      vec3 color = ambient + diffuse + fresnelColor + specularColor;
      
      // Volume-based intensity with smoother response
      float volumeBoost = 1.0 + smoothstep(0.0, 0.4, u_volume) * 0.5;
      color *= volumeBoost;
      
      // Subtle inner glow
      float innerGlow = 1.0 - smoothstep(0.6, 1.0, length(vPos));
      color += goldMid * innerGlow * 0.15;
      
      // Depth-based darkening for better 3D perception
      float depth = gl_FragCoord.z;
      color *= mix(0.8, 1.0, 1.0 - depth);
      
      gl_FragColor = vec4(color, 1.0);
    }
  `;

  // Initialize Three.js scene
  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const renderer = new THREE.WebGLRenderer({ 
      canvas, 
      antialias: true, 
      alpha: false 
    });
    renderer.setClearColor(0x000000, 1);
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(Math.min(2, window.devicePixelRatio));
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.2;

    const camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 0, 4);

    const scene = new THREE.Scene();
    scene.fog = new THREE.Fog(0x000000, 8, 15);

    // Enhanced lighting setup
    const ambientLight = new THREE.AmbientLight(0x1a1a1a, 0.4);
    scene.add(ambientLight);

    const directionalLight1 = new THREE.DirectionalLight(0xffd700, 1.2);
    directionalLight1.position.set(5, 5, 5);
    directionalLight1.castShadow = true;
    directionalLight1.shadow.mapSize.width = 2048;
    directionalLight1.shadow.mapSize.height = 2048;
    directionalLight1.shadow.camera.near = 0.5;
    directionalLight1.shadow.camera.far = 50;
    directionalLight1.shadow.bias = -0.0001;
    scene.add(directionalLight1);

    const directionalLight2 = new THREE.DirectionalLight(0xffaa00, 0.6);
    directionalLight2.position.set(-3, 2, 4);
    scene.add(directionalLight2);

    const pointLight = new THREE.PointLight(0xffd700, 0.8, 10);
    pointLight.position.set(0, 3, 2);
    scene.add(pointLight);

    // Sphere geometry with higher detail
    const geometry = new THREE.SphereGeometry(0.8, 256, 256);
    const uniforms = {
      u_time: { value: 0.0 },
      u_pitch: { value: 0.0 },
      u_volume: { value: 0.0 }
    };

    const material = new THREE.ShaderMaterial({
      uniforms,
      vertexShader,
      fragmentShader,
      side: THREE.FrontSide
    });

    const mesh = new THREE.Mesh(geometry, material);
    mesh.castShadow = true;
    scene.add(mesh);

    // Enhanced ground plane with better shadow reception
    const planeGeometry = new THREE.PlaneGeometry(20, 20);
    const planeMaterial = new THREE.ShadowMaterial({ 
      opacity: 0.3,
      transparent: true
    });
    const plane = new THREE.Mesh(planeGeometry, planeMaterial);
    plane.rotation.x = -Math.PI / 2;
    plane.position.y = -1.5;
    plane.receiveShadow = true;
    scene.add(plane);

    // Add subtle environment reflection
    const envGeometry = new THREE.SphereGeometry(50, 32, 32);
    const envMaterial = new THREE.MeshBasicMaterial({
      color: 0x111111,
      side: THREE.BackSide
    });
    const envSphere = new THREE.Mesh(envGeometry, envMaterial);
    scene.add(envSphere);

    sceneRef.current = { scene, camera, renderer, mesh, uniforms, animationId: 0 };

    const animate = () => {
      if (!sceneRef.current) return;
      
      sceneRef.current.uniforms.u_time.value += 0.005;
      sceneRef.current.renderer.render(sceneRef.current.scene, sceneRef.current.camera);
      sceneRef.current.animationId = requestAnimationFrame(animate);
    };
    animate();

    const handleResize = () => {
      if (!sceneRef.current) return;
      sceneRef.current.camera.aspect = window.innerWidth / window.innerHeight;
      sceneRef.current.camera.updateProjectionMatrix();
      sceneRef.current.renderer.setSize(window.innerWidth, window.innerHeight);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (sceneRef.current) {
        cancelAnimationFrame(sceneRef.current.animationId);
      }
    };
  }, []);

  // Enhanced audio analysis with smoothing
  const analyzeAudio = () => {
    if (!analyserRef.current) return;

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
    analyserRef.current.getByteFrequencyData(dataArray);

    // Calculate volume (RMS)
    let sum = 0;
    for (let i = 0; i < dataArray.length; i++) {
      sum += dataArray[i] * dataArray[i];
    }
    const rms = Math.sqrt(sum / dataArray.length);
    const newVolume = rms / 255;

    // Calculate pitch (dominant frequency)
    let maxIndex = 0;
    let maxValue = 0;
    for (let i = 0; i < dataArray.length; i++) {
      if (dataArray[i] > maxValue) {
        maxValue = dataArray[i];
        maxIndex = i;
      }
    }
    
    const sampleRate = audioContextRef.current?.sampleRate || 44100;
    const nyquist = sampleRate / 2;
    const frequency = (maxIndex / dataArray.length) * nyquist;
    
    // Apply smoothing to reduce jitter
    const smoothingFactor = 0.15;
    smoothedPitchRef.current = smoothedPitchRef.current * (1 - smoothingFactor) + frequency * smoothingFactor;
    smoothedVolumeRef.current = smoothedVolumeRef.current * (1 - smoothingFactor) + newVolume * smoothingFactor;
    
    setPitch(smoothedPitchRef.current);
    setVolume(smoothedVolumeRef.current);

    // Update shader uniforms with smoothed values
    if (sceneRef.current) {
      sceneRef.current.uniforms.u_pitch.value = smoothedPitchRef.current;
      sceneRef.current.uniforms.u_volume.value = smoothedVolumeRef.current;
    }

    animationFrameRef.current = requestAnimationFrame(analyzeAudio);
  };

  // Start/stop voice recognition and audio analysis
  const toggleListening = async () => {
    if (!isSupported) return;

    if (isListening) {
      // Stop listening
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (audioContextRef.current) {
        await audioContextRef.current.close();
        audioContextRef.current = null;
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      setIsListening(false);
      
      // Smooth transition to idle state
      const smoothToZero = () => {
        smoothedPitchRef.current *= 0.95;
        smoothedVolumeRef.current *= 0.95;
        setPitch(smoothedPitchRef.current);
        setVolume(smoothedVolumeRef.current);
        
        if (sceneRef.current) {
          sceneRef.current.uniforms.u_pitch.value = smoothedPitchRef.current;
          sceneRef.current.uniforms.u_volume.value = smoothedVolumeRef.current;
        }
        
        if (smoothedPitchRef.current > 1 || smoothedVolumeRef.current > 0.01) {
          requestAnimationFrame(smoothToZero);
        }
      };
      smoothToZero();
    } else {
      try {
        // Start audio context for pitch detection
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        const source = audioContextRef.current.createMediaStreamSource(stream);
        analyserRef.current = audioContextRef.current.createAnalyser();
        analyserRef.current.fftSize = 4096;
        analyserRef.current.smoothingTimeConstant = 0.8;
        source.connect(analyserRef.current);
        
        analyzeAudio();

        // Start speech recognition (silent)
        const SpeechRecognition = window.SpeechRecognition || (window as any).webkitSpeechRecognition;
        recognitionRef.current = new SpeechRecognition();
        recognitionRef.current.continuous = true;
        recognitionRef.current.interimResults = true;
        recognitionRef.current.start();
        
        setIsListening(true);
      } catch (error) {
        console.error('Error accessing microphone:', error);
        setIsSupported(false);
      }
    }
  };

  // Check for browser support
  useEffect(() => {
    const hasSupport = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
    setIsSupported(hasSupport);
  }, []);

  return (
    <div className="relative min-h-screen bg-black overflow-hidden">
      {/* Canvas */}
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full"
      />
      
      {/* Top Right Stats */}
      <div className="absolute top-6 right-6 z-10">
        <div className="bg-black/70 backdrop-blur-sm rounded-lg p-4 border border-yellow-500/30">
          <div className="text-yellow-400 text-sm font-mono space-y-1">
            <div>Pitch: {Math.round(pitch)} Hz</div>
            <div>Volume: {Math.round(volume * 100)}%</div>
          </div>
        </div>
      </div>

      {/* Bottom Center Control */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <button
          onClick={toggleListening}
          disabled={!isSupported}
          className={`relative inline-flex items-center justify-center w-16 h-16 rounded-full text-white font-semibold transition-all duration-500 ${
            isListening
              ? 'bg-red-500 hover:bg-red-600 shadow-lg shadow-red-500/40'
              : 'bg-yellow-600 hover:bg-yellow-700 shadow-lg shadow-yellow-600/40'
          } ${
            !isSupported ? 'opacity-50 cursor-not-allowed' : 'hover:scale-110 active:scale-95'
          }`}
        >
          {isListening ? <MicOff size={24} /> : <Mic size={24} />}
          {isListening && (
            <div className="absolute inset-0 rounded-full bg-red-500 animate-ping opacity-20" />
          )}
        </button>
      </div>
    </div>
  );
};

export default VoiceVisualizer;