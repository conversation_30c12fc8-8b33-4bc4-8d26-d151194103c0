import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import App from './App';

describe('App', () => {
  it('renders without crashing', () => {
    render(<App />);
    // The app should render the VoiceVisualizer component
    // We can check for elements that should be present
    expect(document.body).toBeTruthy();
  });

  it('contains the main container', () => {
    render(<App />);
    // Check if the main container div is present
    const container = document.querySelector('.relative.min-h-screen.bg-black.overflow-hidden');
    expect(container).toBeTruthy();
  });
});
