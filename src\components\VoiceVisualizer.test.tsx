import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import VoiceVisualizer from './VoiceVisualizer';

// Mock Web APIs that aren't available in jsdom
const mockGetUserMedia = vi.fn();
const mockAudioContext = vi.fn();
const mockSpeechRecognition = vi.fn();

// Mock navigator.mediaDevices
Object.defineProperty(navigator, 'mediaDevices', {
  writable: true,
  value: {
    getUserMedia: mockGetUserMedia,
  },
});

// Mock AudioContext
(global as any).AudioContext = mockAudioContext;
(global as any).webkitAudioContext = mockAudioContext;

// Mock SpeechRecognition
(global as any).SpeechRecognition = mockSpeechRecognition;
(global as any).webkitSpeechRecognition = mockSpeechRecognition;

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('VoiceVisualizer', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  it('renders without crashing', () => {
    render(<VoiceVisualizer />);
    expect(document.body).toBeTruthy();
  });

  it('displays the settings button', () => {
    render(<VoiceVisualizer />);
    const settingsButton = screen.getByRole('button');
    expect(settingsButton).toBeTruthy();
  });

  it('opens settings modal when settings button is clicked', async () => {
    render(<VoiceVisualizer />);
    const settingsButton = screen.getByRole('button');
    
    fireEvent.click(settingsButton);
    
    await waitFor(() => {
      expect(screen.getByText('Settings')).toBeTruthy();
    });
  });

  it('displays microphone button in voice mode', () => {
    render(<VoiceVisualizer />);
    // Look for microphone icon (Mic component)
    const micButton = document.querySelector('button[class*="bg-yellow-600"]');
    expect(micButton).toBeTruthy();
  });

  it('shows error message when speech recognition is not supported', () => {
    // Mock unsupported browser
    delete (global as any).SpeechRecognition;
    delete (global as any).webkitSpeechRecognition;
    delete (navigator as any).mediaDevices;
    
    render(<VoiceVisualizer />);
    
    // Should show error message about browser support
    expect(document.body.textContent).toContain('Speech recognition not supported');
  });

  it('loads settings from localStorage on mount', () => {
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'openai_api_key') return 'test-api-key';
      if (key === 'openai_model') return 'gpt-4';
      if (key === 'chatbot_mode') return 'true';
      return null;
    });

    render(<VoiceVisualizer />);
    
    expect(localStorageMock.getItem).toHaveBeenCalledWith('openai_api_key');
    expect(localStorageMock.getItem).toHaveBeenCalledWith('openai_model');
    expect(localStorageMock.getItem).toHaveBeenCalledWith('chatbot_mode');
  });
});
