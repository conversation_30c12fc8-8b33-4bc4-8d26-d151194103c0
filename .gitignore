# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
dist-ssr/
*.local

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Logs
logs/
*.log

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Duplicate project directories (to be removed)
<PERSON> (2)/
<PERSON> (3)/
glassy-blob-voice/

# Test files
test-*.html
*.test.html

# API keys and sensitive data
*.key
*.pem
.env.keys
