import React, { useRef, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Environment } from '@react-three/drei';
import * as THREE from 'three';

// Use named export for noise
import { createNoise4D } from 'simplex-noise';
const noise4D = createNoise4D();

function GlacisphereMesh({ intensity = 0.5, speed = 0.012, ampBase = 0.12, ampVar = 0.18 }) {
  const mesh = useRef();
  const origPositions = useRef();
  const tRef = useRef(0);

  useEffect(() => {
    if (mesh.current) {
      const pos = mesh.current.geometry.attributes.position.array;
      origPositions.current = new Float32Array(pos);
    }
  }, []);

  useFrame(() => {
    tRef.current += speed;
    const t = tRef.current;
    const geometry = mesh.current.geometry;
    const pos = geometry.attributes.position;
    if (!origPositions.current) return;
    for (let i = 0; i < pos.count; i++) {
      const ox = origPositions.current[i * 3];
      const oy = origPositions.current[i * 3 + 1];
      const oz = origPositions.current[i * 3 + 2];
      const len = Math.sqrt(ox * ox + oy * oy + oz * oz);
      const nx = ox / len;
      const ny = oy / len;
      const nz = oz / len;
      // Use layered noise for more organic, fluid motion
      const n1 = noise4D(nx * 1.5, ny * 1.5, nz * 1.5, t * 0.7);
      const n2 = noise4D(nx * 2.5, ny * 2.5, nz * 2.5, t * 1.2);
      const n3 = noise4D(nx * 4.5, ny * 4.5, nz * 4.5, t * 2.2);
      const noise = (n1 * 0.6 + n2 * 0.3 + n3 * 0.1);
      const amp = ampBase + ampVar * noise;
      const r = 1.2 * (1 + amp * intensity);
      pos.setXYZ(i, nx * r, ny * r, nz * r);
    }
    pos.needsUpdate = true;
    geometry.computeVertexNormals();
  });

  return (
    <mesh ref={mesh} position={[0, 0, 0]}>
      {/* High-res sphere for smoothness */}
      <sphereGeometry args={[1.2, 128, 128]} />
      <meshPhysicalMaterial
        color={new THREE.Color('#FFD700')}
        roughness={0.08}
        metalness={0.6}
        transmission={0.7}
        thickness={1.1}
        ior={1.45}
        reflectivity={0.7}
        clearcoat={0.7}
        clearcoatRoughness={0.15}
        attenuationColor={new THREE.Color('#FFC300')}
        attenuationDistance={0.7}
        transparent
        opacity={0.98}
      />
    </mesh>
  );
}

export default function Glacisphere({ intensity = 0.5, mode = 'idle', speed, ampBase, ampVar }) {
  return (
    <div style={{ width: 420, height: 420, borderRadius: '50%', overflow: 'hidden', background: 'black', margin: 'auto', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
      <Canvas camera={{ position: [0, 0, 3.5], fov: 50 }} style={{ background: 'black' }}>
        <ambientLight intensity={0.8} />
        <pointLight position={[10, 10, 10]} intensity={1.5} color={'#fff8e1'} />
        <GlacisphereMesh intensity={intensity} speed={speed} ampBase={ampBase} ampVar={ampVar} />
        <Environment preset="city" />
      </Canvas>
    </div>
  );
} 